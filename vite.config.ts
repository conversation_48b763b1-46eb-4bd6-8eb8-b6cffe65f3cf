import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api/reasoning': {
        target: process.env.VITE_REASONING_API_URL || 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/reasoning/, '')
      },
      '/api/embedding': {
        target: process.env.VITE_EMBEDDING_API_URL || 'https://ark.cn-beijing.volces.com/api/v3/embeddings',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/api\/embedding/, '')
      },
      '/api/web-proxy': {
        target: 'http://localhost:9527',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api\/web-proxy/, '/api/web-proxy')
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})