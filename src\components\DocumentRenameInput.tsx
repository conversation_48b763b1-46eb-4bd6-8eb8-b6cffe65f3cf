import React, { useState, useEffect, useRef } from 'react'
import { Check, X } from 'lucide-react'

interface DocumentRenameInputProps {
  initialName: string
  onSave: (newName: string) => boolean
  onCancel: () => void
  className?: string
  placeholder?: string
}

export const DocumentRenameInput: React.FC<DocumentRenameInputProps> = ({
  initialName,
  onSave,
  onCancel,
  className = '',
  placeholder = 'Enter document name...'
}) => {
  const [name, setName] = useState(initialName)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    // Focus and select all text when component mounts
    if (inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [])

  const handleSave = async () => {
    const trimmedName = name.trim()
    
    // Validate name
    if (!trimmedName) {
      setError('Name cannot be empty')
      return
    }
    
    if (trimmedName.length > 255) {
      setError('Name is too long (max 255 characters)')
      return
    }
    
    setIsLoading(true)
    setError('')
    
    try {
      const success = onSave(trimmedName)
      if (success) {
        // Success handled by parent component
      } else {
        setError('Failed to rename document')
        setIsLoading(false)
      }
    } catch (err) {
      setError('An error occurred while renaming')
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setName(initialName)
    setError('')
    onCancel()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value)
    if (error) setError('') // Clear error when user starts typing
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex-1 relative">
        <input
          ref={inputRef}
          type="text"
          value={name}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={isLoading}
          className={`
            w-full px-2 py-1 text-sm border rounded
            focus:outline-none focus:ring-2 focus:ring-blue-500
            disabled:opacity-50 disabled:cursor-not-allowed
            ${error ? 'border-red-500' : 'border-gray-300'}
            ${isLoading ? 'bg-gray-50' : 'bg-white'}
          `}
          maxLength={255}
        />
        {error && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-500 whitespace-nowrap">
            {error}
          </div>
        )}
      </div>
      
      <div className="flex items-center gap-1">
        <button
          onClick={handleSave}
          disabled={isLoading || !name.trim()}
          className="
            p-1 rounded hover:bg-green-100 text-green-600
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors duration-200
          "
          title="Save"
        >
          <Check size={16} />
        </button>
        
        <button
          onClick={handleCancel}
          disabled={isLoading}
          className="
            p-1 rounded hover:bg-red-100 text-red-600
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors duration-200
          "
          title="Cancel"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  )
}

export default DocumentRenameInput