// Document type for text-based files and web content
export interface Document {
  id: string
  name: string
  type: 'txt' | 'md' | 'web'
  size: number
  content: string
  uploadedAt: Date
  sourceUrl?: string    // URL source for web documents
  excerpt?: string      // Content preview for web documents
}

// Upload result type
export interface UploadResult {
  success: boolean
  document?: Document
  error?: string
}

// Component prop types
// Removed BaseComponentProps - use React's built-in prop types directly

// Chat-related types
export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  searchResults?: SearchResult[]
  reasoning?: string
}

export interface DocumentChunk {
  id: string
  documentId: string
  documentName: string
  content: string
  startIndex: number
  endIndex: number
  embedding?: number[]
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  documentIds: string[]
  isFavorite: boolean
  createdAt: Date
  updatedAt: Date
}

export interface SearchResult {
  chunk: DocumentChunk
  similarity: number
}

// Web extraction types
export interface WebExtractionResult {
  success: boolean
  title?: string
  content?: string
  excerpt?: string
  url: string
  error?: string
}

export interface WebExtractionOptions {
  maxContentLength?: number
  timeout?: number
  auth?: {
    username: string
    password: string
  }
}