import React, { useState, useRef } from 'react'
import { Send, Paperclip, X } from 'lucide-react'
import { Document } from '@/types'
import DocumentManagerModal from './DocumentManagerModal'
import { cn } from '@/utils/cn'

interface ChatInputProps {
  onSendMessage: (message: string) => void
  disabled: boolean
  placeholder?: string
  documents: Document[]
  selectedDocumentIds: string[]
  onDocumentSelectionChange: (documentIds: string[]) => void
  onDocumentsUpdate?: () => void
}

export const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled,
  placeholder = "Ask a question...",
  documents,
  selectedDocumentIds,
  onDocumentSelectionChange,
  onDocumentsUpdate
}) => {
  const [message, setMessage] = useState('')
  const [showAttachmentModal, setShowAttachmentModal] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim() && !disabled) {
      onSendMessage(message.trim())
      setMessage('')
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'
      }
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value)
  }

  const handleRemoveDocument = (documentId: string) => {
    const updatedIds = selectedDocumentIds.filter(id => id !== documentId)
    onDocumentSelectionChange(updatedIds)
  }

  const selectedDocuments = selectedDocumentIds
    .map(id => documents.find(d => d.id === id))
    .filter((doc): doc is Document => doc !== null)

  return (
    <div className="border-t bg-white">
      {/* Document Tags */}
      {selectedDocuments.length > 0 && (
        <div className="px-4 pt-3 pb-2">
          <div className="flex flex-wrap gap-2">
            {selectedDocuments.map((doc) => (
              <div
                key={doc.id}
                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                <span className="truncate max-w-32">{doc.name}</span>
                <button
                  onClick={() => handleRemoveDocument(doc.id)}
                  className="hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                  disabled={disabled}
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex gap-2 items-end relative">
          {/* Attachment Button */}
          <button
            type="button"
            onClick={() => setShowAttachmentModal(true)}
            disabled={disabled}
            className={cn(
              "p-2 rounded-lg border transition-colors flex-shrink-0",
              "bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            title="Manage Documents"
          >
            <Paperclip className="w-4 h-4" />
          </button>



          {/* Message Input */}
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleTextareaChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 resize-none overflow-hidden min-h-[40px] max-h-[120px]"
          />

          {/* Send Button */}
          <button
            type="submit"
            disabled={disabled || !message.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 flex-shrink-0"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </form>

      {/* Attachment Modal */}
      <DocumentManagerModal
        isOpen={showAttachmentModal}
        onClose={() => setShowAttachmentModal(false)}
        documents={documents}
        selectedDocumentIds={selectedDocumentIds}
        onSelectionChange={onDocumentSelectionChange}
        onDocumentsUpdate={onDocumentsUpdate || (() => {})}
      />
    </div>
  )
}