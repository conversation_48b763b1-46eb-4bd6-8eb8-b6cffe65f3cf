# ChatDoc UI Redesign - Technical Architecture Document

## 1. Architecture Design

```mermaid
graph TD
    A[User Browser] --> B[React Frontend Application]
    B --> C[Main Chat Interface Component]
    C --> D[Left Sidebar Component]
    C --> E[Chat Display Area]
    C --> F[Enhanced Input Component]
    F --> G[Attachment Modal Component]
    G --> H[Document Upload Service]
    G --> I[Web Extraction Service]
    B --> J[Chat Service]
    B --> K[Document Storage Service]
    J --> L[LLM API Service]
    K --> M[LocalStorage]

    subgraph "Frontend Layer"
        B
        C
        D
        E
        F
        G
    end

    subgraph "Service Layer"
        H
        I
        J
        K
    end

    subgraph "Storage Layer"
        M
    end

    subgraph "External Services"
        L
    end
```

## 2. Technology Description

- Frontend: React@18 + TypeScript + Tailwind CSS@3 + Vite
- State Management: React hooks (useState, useEffect) + Custom hooks
- Storage: LocalStorage for documents and chat sessions
- Icons: Lucide React
- Routing: React Router (simplified to single route)

## 3. Route Definitions

| Route | Purpose |
|-------|----------|
| / | Main chat interface (default and only route) |
| * | Redirect to main chat interface |

## 4. Component Architecture

### 4.1 Component Hierarchy

```
App.tsx (Root)
├── ErrorBoundary
└── MainChatInterface (replaces Layout + ChatPage)
    ├── ChatSidebar
    │   ├── NewChatButton
    │   ├── ChatHistorySection
    │   └── FavoritesSection
    ├── ChatDisplayArea
    │   ├── MessageBubble[]
    │   ├── AIThinkingIndicator
    │   └── CollapsibleSources
    └── EnhancedChatInput
        ├── DocumentTags
        ├── AttachmentButton
        ├── MessageTextarea
        ├── SendButton
        └── AttachmentModal
            ├── FileUploadArea
            ├── UrlInput
            └── DocumentSelector
```

### 4.2 New Components Required

**AttachmentModal Component:**
```typescript
interface AttachmentModalProps {
  isOpen: boolean
  onClose: () => void
  documents: Document[]
  selectedDocumentIds: string[]
  onDocumentSelectionChange: (ids: string[]) => void
  onDocumentUpload: (files: FileList) => Promise<void>
  onUrlExtraction: (url: string) => Promise<void>
}
```

**MainChatInterface Component:**
```typescript
interface MainChatInterfaceProps {
  // Combines functionality from Layout and ChatPage
  // Manages global chat state and document management
}
```

### 4.3 Modified Components

**App.tsx Changes:**
- Remove React Router Routes
- Remove imports for HomePage, DocumentsPage, SettingsPage
- Replace Layout + Routes with MainChatInterface

**EnhancedChatInput Changes:**
- Replace plus button popup with attachment modal trigger
- Add multi-line textarea support
- Integrate AttachmentModal component
- Update styling for bottom-fixed positioning

**ChatPage.tsx → MainChatInterface.tsx:**
- Remove navigation dependencies
- Integrate sidebar directly into component
- Add favorites functionality to chat sessions
- Update styling for full-screen layout

## 5. Data Model

### 5.1 Data Model Definition

```mermaid
erDiagram
    CHAT_SESSION ||--o{ CHAT_MESSAGE : contains
    CHAT_SESSION ||--o{ DOCUMENT : references
    CHAT_MESSAGE ||--o{ SEARCH_RESULT : includes

    CHAT_SESSION {
        string id PK
        string title
        string[] documentIds
        boolean isFavorite
        Date createdAt
        Date updatedAt
    }
    
    CHAT_MESSAGE {
        string id PK
        string sessionId FK
        string role
        string content
        string reasoning
        Date timestamp
        SearchResult[] searchResults
    }
    
    DOCUMENT {
        string id PK
        string name
        string type
        number size
        Date uploadedAt
        string content
        number[] embeddings
    }
    
    SEARCH_RESULT {
        string documentId FK
        string content
        number score
        number startIndex
        number endIndex
    }
```

### 5.2 Data Definition Language

**Extended ChatSession Interface:**
```typescript
interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  documentIds: string[]
  isFavorite: boolean // NEW FIELD
  createdAt: Date
  updatedAt: Date
}
```

**LocalStorage Schema Updates:**
```typescript
// Add favorites management
const FAVORITES_STORAGE_KEY = 'chatdoc_favorites'

// Update session storage to include favorites flag
interface StoredSession extends ChatSession {
  isFavorite: boolean
}
```

## 6. Implementation Plan

### 6.1 Phase 1: Core Structure
1. Update App.tsx to remove routing and use MainChatInterface
2. Create MainChatInterface component from ChatPage.tsx
3. Update component imports and remove unused pages
4. Test basic chat functionality

### 6.2 Phase 2: Enhanced Input
1. Create AttachmentModal component
2. Update EnhancedChatInput with attachment functionality
3. Integrate file upload and URL extraction
4. Add multi-line textarea support

### 6.3 Phase 3: Sidebar Enhancements
1. Add favorites functionality to chat sessions
2. Update ChatSidebar with favorites section
3. Implement session favoriting/unfavoriting
4. Update localStorage to persist favorites

### 6.4 Phase 4: Polish & Testing
1. Update styling for full-screen layout
2. Add responsive design improvements
3. Test all existing functionality
4. Performance optimization

## 7. Integration Points

### 7.1 Existing Services Integration
- **ChatService**: No changes required, maintains all existing functionality
- **DocumentApiService**: Integrate into AttachmentModal for file uploads
- **WebExtractionService**: Integrate into AttachmentModal for URL processing
- **EmbeddingService**: No changes required
- **StorageService**: Extend to support favorites functionality

### 7.2 State Management
- **useChatState Hook**: Extend to include favorites management
- **Document State**: Move from page-level to application-level
- **Modal State**: Add attachment modal state management

### 7.3 Styling Integration
- Maintain existing Tailwind CSS patterns
- Ensure consistent spacing and color schemes
- Preserve existing component styling where possible
- Add new utility classes for full-screen layout
