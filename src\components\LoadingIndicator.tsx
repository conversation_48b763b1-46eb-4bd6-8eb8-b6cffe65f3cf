import React from 'react'
import { cn } from '@/utils/cn'

const LoadingIndicator: React.FC<{
  message?: string
  className?: string
}> = ({ 
  message = "AI is thinking...", 
  className 
}) => {
  return (
    <div className={cn('flex items-center gap-3 p-4', className)}>
      <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      </div>
      
      <div className="max-w-[70%] rounded-lg px-4 py-2 bg-gray-100 text-gray-900">
        <div className="flex items-center gap-2">
          <span>{message}</span>
          <div className="flex gap-1">
            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoadingIndicator