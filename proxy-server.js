const express = require('express');
const cors = require('cors');
const https = require('https');
const http = require('http');
const { URL } = require('url');

const app = express();
const PORT = 9527;

// Enable CORS for the frontend
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Proxy server is running' });
});

// Simple proxy endpoint for web content extraction
app.get('/api/web-proxy', (req, res) => {
  const targetUrl = req.query.url;
  
  if (!targetUrl) {
    return res.status(400).json({ error: 'Missing url parameter' });
  }

  try {
    const parsedUrl = new URL(targetUrl);
    const isHttps = parsedUrl.protocol === 'https:';
    const httpModule = isHttps ? https : http;
    
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.pathname + parsedUrl.search,
      method: req.method,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    };
    
    // Forward authentication headers if present
    if (req.headers.authorization) {
      options.headers['Authorization'] = req.headers.authorization;
    }
    
    // Forward cookies if present
    if (req.headers.cookie) {
      options.headers['Cookie'] = req.headers.cookie;
    }
    
    console.log(`Proxying request to: ${targetUrl}`);
    console.log('Request options:', JSON.stringify(options, null, 2));

    const proxyReq = httpModule.request(options, (proxyRes) => {
      console.log(`Response status: ${proxyRes.statusCode} for ${targetUrl}`);
      // Set CORS headers
      res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      
      // Forward status code
      res.statusCode = proxyRes.statusCode;
      
      // Forward headers (except some that might cause issues)
      // Note: We keep content-encoding to let the browser handle decompression
      Object.keys(proxyRes.headers).forEach(key => {
        if (!['connection', 'transfer-encoding'].includes(key.toLowerCase())) {
          res.setHeader(key, proxyRes.headers[key]);
        }
      });
      
      // Pipe the response
      proxyRes.pipe(res);
    });
    
    proxyReq.on('error', (err) => {
      console.error('Proxy request error:', err.message);
      console.error('Error details:', { code: err.code, errno: err.errno, syscall: err.syscall });
      if (!res.headersSent) {
        let errorMessage = 'Proxy request failed';
        if (err.code === 'ENOTFOUND') {
          errorMessage = 'Website not found. Please check the URL.';
        } else if (err.code === 'ECONNREFUSED') {
          errorMessage = 'Connection refused. The website may be down.';
        } else if (err.code === 'ETIMEDOUT') {
          errorMessage = 'Connection timed out. The website may be slow or unresponsive.';
        }
        res.status(500).json({
          error: errorMessage,
          details: err.message,
          code: err.code
        });
      }
    });
    
    // Set timeout (configurable via query parameter, default 45 seconds)
    // Proxy timeout should be longer than client timeout to avoid race conditions
    const timeoutMs = parseInt(req.query.timeout) || 45000;
    proxyReq.setTimeout(timeoutMs, () => {
      console.log(`Request timeout after ${timeoutMs}ms for URL: ${targetUrl}`);
      proxyReq.destroy();
      if (!res.headersSent) {
        res.status(408).json({
          error: 'Request timeout',
          timeout: timeoutMs,
          message: `The server took longer than ${timeoutMs/1000} seconds to respond. The website may be slow or unresponsive.`
        });
      }
    });
    
    proxyReq.end();
    
  } catch (error) {
    console.error('Invalid URL:', targetUrl, error.message);
    res.status(400).json({ error: 'Invalid URL format' });
  }
});

// Handle preflight requests
app.options('/api/web-proxy', (req, res) => {
  res.setHeader('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err.message);
  res.status(500).json({ error: 'Internal server error' });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

app.listen(PORT, () => {
  console.log(`Proxy server running on http://localhost:${PORT}`);
});