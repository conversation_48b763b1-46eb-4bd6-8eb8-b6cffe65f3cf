# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ChatDoc is a React-based AI document chat assistant that allows users to upload documents and chat with them using AI. The application uses a client-side RAG (Retrieval-Augmented Generation) architecture with vector embeddings for document search and LLM integration for responses.

## Development Commands

### Essential Commands
- `npm run dev` - Start development server (opens at http://localhost:3000)
- `npm run build` - Build for production (TypeScript compilation + Vite build)
- `npm run lint` - Run ESLint with TypeScript support
- `npm run test` - Run Vitest test suite
- `npm run test:ui` - Run tests with UI interface
- `npm run preview` - Preview production build

### Environment Setup
Copy `.env.example` to `.env` and configure:
- `VITE_REASONING_API_URL` - LLM API endpoint (default: DeepSeek)
- `VITE_REASONING_API_KEY` - LLM API key
- `VITE_REASONING_MODEL` - Model name (default: deepseek-chat)
- `VITE_EMBEDDING_API_URL` - Embedding API endpoint (default: Doubao)
- `VITE_EMBEDDING_API_KEY` - Embedding API key
- `VITE_EMBEDDING_MODEL` - Embedding model (default: doubao-embedding-v1)

## Architecture

### Core Architecture Pattern
- **Client-side RAG**: Document processing, embedding generation, and similarity search happen entirely in the browser
- **Service Layer**: Modular services for chat logic, document management, and embedding operations
- **State Management**: React hooks + localStorage for persistence
- **Type Safety**: Full TypeScript with strict configuration

### Key Architectural Components

#### 1. Document Processing Pipeline
```
File Upload → Text Extraction → Chunking → Embedding Generation → Vector Storage (localStorage)
```

#### 2. Chat RAG Flow
```
User Query → Embedding Generation → Vector Similarity Search → Context Assembly → LLM Request → Response
```

#### 3. Service Layer Architecture
- **ChatService** (`src/services/chatService.ts`): RAG logic, LLM integration, chat session management
- **EmbeddingService** (`src/services/embeddingService.ts`): Vector embeddings, similarity search, chunking
- **DocumentApiService** (`src/services/documentApiService.ts`): Document CRUD operations (future server integration)
- **StorageService** (`src/utils/storageService.ts`): Unified localStorage abstraction

### Key Design Patterns

#### Path Aliases
- Use `@/` for all src imports (configured in vite.config.ts and tsconfig.json)
- Example: `import { ChatService } from '@/services/chatService'`

#### API Configuration
- Centralized API configuration in `src/utils/apiConfig.ts`
- Supports multiple LLM providers (DeepSeek, OpenAI, etc.)
- Environment-based configuration with fallbacks

#### Storage Strategy
- Documents and embeddings stored in localStorage as JSON
- Chat sessions persisted with full conversation history
- Automatic data serialization/deserialization with type safety

### Component Structure

#### Page Components
- **HomePage**: Landing page with feature overview
- **DocumentsPage**: Document management (upload, view, delete)
- **ChatPage**: Main chat interface with document selection and RAG chat
- **NotFoundPage**: 404 error handling

#### UI Components
- **Layout**: Application shell with Header and Footer
- **ErrorBoundary**: Global error handling
- **DocumentSelector**: Multi-document selection for chat sessions
- **CollapsibleSources**: Expandable source citation display
- **LoadingIndicator**: Consistent loading states

### State Management Patterns

#### Custom Hooks
- **useChatState** (`src/hooks/useChatState.ts`): Chat session management, message history, document context

#### Data Flow
```
User Action → Component State → Service Layer → Storage/API → State Update → UI Refresh
```

## Important Implementation Notes

### Vector Embeddings
- Client-side embedding generation using configurable APIs
- Cosine similarity search with configurable threshold (0.3 default)
- Document chunking with overlap for better context retrieval
- Embeddings stored with document chunks for efficient lookup

### Chat System
- Session-based conversations with document context
- Automatic title generation from first user message
- Source attribution with similarity scores
- Configurable context window (5 chunks max, 6 message history)

### Type System
Key types in `src/types/index.ts`:
- **Document**: File metadata and content
- **ChatMessage**: Message with role, content, sources, and reasoning
- **DocumentChunk**: Text chunks with embeddings and positional data
- **ChatSession**: Conversation container with document associations

### Error Handling
- Global ErrorBoundary for React errors
- Service-level error handling with user-friendly messages
- API configuration validation before requests
- Graceful degradation for missing API credentials

## Testing Strategy

- **Vitest** for unit testing
- Test configuration in `vitest.config.ts`
- Focus on service layer logic and utility functions
- UI testing through component interaction tests

## Build Configuration

- **Vite** build tool with React plugin
- **TypeScript** with strict mode enabled
- **Tailwind CSS** for styling with custom color palette
- **ESLint** with React and TypeScript rules
- Source maps enabled for debugging
- Path mapping for clean imports

## Styling Approach

- **Tailwind CSS** utility-first approach
- Custom color palette: `primary.100` (light blue), `primary.600` (blue)
- Inter font family system
- Component-scoped styling patterns
- Responsive design with mobile-first approach

## API Integration

### LLM Integration
- Configurable provider support (DeepSeek, OpenAI, etc.)
- Standardized request/response format
- Error handling with fallback messages
- Context assembly with document sources and chat history

### Future Server Integration
- DocumentApiService prepared for server-side document management
- RESTful API patterns for CRUD operations
- File upload handling with FormData
- Document sharing and permissions (planned)

## Development Guidelines

### Code Organization
- Feature-based organization in services and components
- Shared utilities in utils directory
- Type definitions centralized in types directory
- Custom hooks for reusable stateful logic

### Import Patterns
- Use path aliases (@/) for all internal imports
- Group imports: React/external libraries → internal components → types → utils
- Prefer named exports over default exports except for pages and main components

### Error Boundaries
- All async operations wrapped in try-catch blocks
- User-facing error messages for API failures
- Console logging for debugging information
- Graceful fallbacks for non-critical features