import { useState } from 'react'
import { ChatSession, Document } from '@/types'

export function useChatState() {
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [documents, setDocuments] = useState<Document[]>([])
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([])
  const [isLoading, setLoading] = useState(false)
  const [isProcessing, setProcessing] = useState(false)
  
  const resetState = () => {
    setSessions([])
    setCurrentSession(null)
    setDocuments([])
    setSelectedDocumentIds([])
    setLoading(false)
    setProcessing(false)
  }
  
  return {
    // State
    sessions,
    currentSession,
    documents,
    selectedDocumentIds,
    isLoading,
    isProcessing,
    // Actions
    setSessions,
    setCurrentSession,
    setDocuments,
    setSelectedDocumentIds,
    setLoading,
    setProcessing,
    resetState
  }
}