import { Readability } from '@mozilla/readability';
import { convertHtmlToMarkdown } from 'dom-to-semantic-markdown';
import { WebExtractionResult, WebExtractionOptions } from '../types';

/**
 * Validates if a URL is properly formatted
 */
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Extracts content from a URL with retry logic and better error handling
 */
async function extractWebContentAttempt(
  url: string,
  options: WebExtractionOptions,
  attemptNumber: number = 1
): Promise<WebExtractionResult> {
  const { maxContentLength = 50000, timeout = 30000, auth } = options;

  // Use longer timeout for retry attempts
  const actualTimeout = attemptNumber > 1 ? timeout * 1.5 : timeout;

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), actualTimeout);

    // Proxy server timeout should be shorter than client timeout
    const proxyTimeout = Math.max(actualTimeout - 5000, 30000);
    let proxyUrl = `/api/web-proxy?url=${encodeURIComponent(url)}&timeout=${proxyTimeout}`;

    // Add authentication parameters if provided
    if (auth && auth.username && auth.password) {
      proxyUrl += `&username=${encodeURIComponent(auth.username)}&password=${encodeURIComponent(auth.password)}`;
    }

    console.log(`Attempt ${attemptNumber}: Fetching ${url} with timeout ${actualTimeout}ms`);

    const response = await fetch(proxyUrl, { signal: controller.signal });
    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text().catch(() => '');
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { error: response.statusText };
      }

      if (response.status === 408) {
        return {
          success: false,
          url,
          error: errorData.message || `Request timeout after ${proxyTimeout}ms. The website may be slow or unresponsive.`
        };
      }

      return {
        success: false,
        url,
        error: errorData.error || `Failed to fetch content (${response.status}: ${response.statusText})`
      };
    }

    const html = await response.text();

    if (!html || html.trim().length === 0) {
      return { success: false, url, error: 'Received empty response from the website' };
    }

    const doc = new DOMParser().parseFromString(html, 'text/html');

    const reader = new Readability(doc);
    const article = reader.parse();

    if (!article) {
      return { success: false, url, error: 'Could not extract readable content from the page' };
    }

    let markdownContent = convertHtmlToMarkdown(article.content || '');

    if (markdownContent.length > maxContentLength) {
      markdownContent = markdownContent.substring(0, maxContentLength) + '\n\n[Content truncated...]';
    }

    const excerpt = (article.textContent || '')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 200) + (markdownContent.length > 200 ? '...' : '');

    console.log(`Successfully extracted content from ${url} on attempt ${attemptNumber}`);

    return {
      success: true,
      title: article.title || 'Untitled',
      content: markdownContent,
      excerpt,
      url
    };

  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          success: false,
          url,
          error: `Request timeout after ${actualTimeout}ms. The website may be slow or unresponsive.`
        };
      }

      console.error(`Attempt ${attemptNumber} failed for ${url}:`, error.message);

      return {
        success: false,
        url,
        error: error.message
      };
    }
    return {
      success: false,
      url,
      error: 'Extraction failed due to an unknown error'
    };
  }
}

/**
 * Extracts content from a URL and converts it to Markdown with retry logic
 */
export async function extractWebContent(
  url: string,
  options: WebExtractionOptions = {}
): Promise<WebExtractionResult> {
  if (!isValidUrl(url)) {
    return { success: false, url, error: 'Invalid URL format' };
  }

  const maxRetries = 2;
  let lastError = '';

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const result = await extractWebContentAttempt(url, options, attempt);

    if (result.success) {
      return result;
    }

    lastError = result.error || 'Unknown error';

    // Don't retry on certain errors
    if (result.error?.includes('Invalid URL') ||
        result.error?.includes('not found') ||
        result.error?.includes('403') ||
        result.error?.includes('401') ||
        result.error?.includes('404')) {
      break;
    }

    // Wait before retry (exponential backoff with jitter)
    if (attempt < maxRetries) {
      const delay = Math.min(1000 * Math.pow(2, attempt - 1) + Math.random() * 1000, 5000);
      console.log(`Waiting ${delay}ms before retry attempt ${attempt + 1}`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return {
    success: false,
    url,
    error: `Failed after ${maxRetries} attempts. Last error: ${lastError}`
  };
}

/**
 * Creates a web document from extracted content
 */
export function createWebDocument(extractionResult: WebExtractionResult, id?: string) {
  if (!extractionResult.success || !extractionResult.content) {
    throw new Error('Cannot create document from failed extraction');
  }

  return {
    id: id || crypto.randomUUID(),
    name: extractionResult.title || 'Web Content',
    type: 'web' as const,
    size: extractionResult.content.length,
    content: extractionResult.content,
    uploadedAt: new Date(),
    sourceUrl: extractionResult.url,
    excerpt: extractionResult.excerpt || ''
  };
}