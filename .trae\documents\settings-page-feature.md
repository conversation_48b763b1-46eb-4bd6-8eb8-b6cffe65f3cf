# Settings Page Feature - Technical Design Document

## 1. Product Overview
A Settings page that allows users to configure LLM API settings through a user-friendly interface instead of manual `.env` file editing. The feature provides secure configuration management for both Reasoning and Embedding LLM services with localStorage persistence and backward compatibility.

## 2. Core Features

### 2.1 User Roles
No role distinction required - all users can access and modify settings.

### 2.2 Feature Module
Our Settings feature consists of the following main components:
1. **Settings Page**: Configuration forms, validation, reset functionality
2. **Settings Storage**: localStorage management, fallback to environment variables
3. **Service Integration**: Updated ChatService and EmbeddingService with dynamic configuration
4. **Navigation Integration**: Settings link in main navigation

### 2.3 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| Settings Page | Reasoning LLM Config | Configure API URL, API Key (masked input), Model selection with validation |
| Settings Page | Embedding LLM Config | Configure API URL, API Key (masked input), Model selection with validation |
| Settings Page | Form Actions | Save settings, Reset to defaults, Form validation with error messages |
| Settings Page | Security Features | Masked API key inputs, secure localStorage storage |

## 3. Core Process

**Settings Configuration Flow:**
1. User navigates to Settings page from main navigation
2. Form loads with current settings (localStorage or defaults)
3. User modifies configuration values
4. Form validates inputs (required fields, URL format)
5. User saves settings to localStorage
6. Services automatically use new settings for API calls
7. User can reset to defaults if needed

```mermaid
graph TD
    A[Main Navigation] --> B[Settings Page]
    B --> C[Load Current Settings]
    C --> D[Display Configuration Form]
    D --> E[User Modifies Settings]
    E --> F[Form Validation]
    F --> G{Valid?}
    G -->|No| H[Show Error Messages]
    H --> E
    G -->|Yes| I[Save to localStorage]
    I --> J[Update Services]
    J --> K[Show Success Message]
    D --> L[Reset to Defaults]
    L --> M[Clear localStorage]
    M --> C
```

## 4. User Interface Design

### 4.1 Design Style
- **Primary Colors**: Blue (#3B82F6) for primary actions, Gray (#6B7280) for secondary
- **Button Style**: Rounded corners (rounded-md), consistent with existing design
- **Font**: System font stack, text-sm for labels, text-base for inputs
- **Layout Style**: Card-based layout with sections, consistent spacing (space-y-6)
- **Icons**: Heroicons for consistency with existing components

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| Settings Page | Page Header | Title "Settings", breadcrumb navigation |
| Settings Page | Reasoning LLM Section | Card with title, 3 input fields (URL, API Key, Model), labels and validation |
| Settings Page | Embedding LLM Section | Card with title, 3 input fields (URL, API Key, Model), labels and validation |
| Settings Page | Action Buttons | Save button (primary blue), Reset button (secondary gray), proper spacing |
| Settings Page | Feedback | Success/error toast messages, inline validation errors |

### 4.3 Responsiveness
Desktop-first design with mobile-adaptive layout using Tailwind responsive classes. Touch-friendly button sizes and proper spacing for mobile devices.

## 5. Technical Architecture

### 5.1 Architecture Design

```mermaid
graph TD
    A[Settings Page Component] --> B[Settings Storage Utility]
    B --> C[localStorage]
    B --> D[Environment Variables]
    A --> E[Form Validation]
    F[ChatService] --> B
    G[EmbeddingService] --> B
    
    subgraph "Frontend Layer"
        A
        E
    end
    
    subgraph "Storage Layer"
        B
        C
        D
    end
    
    subgraph "Service Layer"
        F
        G
    end
```

### 5.2 Technology Stack
- **Frontend**: React@18 + TypeScript + Tailwind CSS
- **Storage**: localStorage with environment variable fallback
- **Validation**: Built-in HTML5 validation + custom URL validation
- **State Management**: React useState hooks

### 5.3 Implementation Tasks

#### Phase 1: Core Infrastructure
1. **Create Settings Types** - Define TypeScript interfaces for settings
2. **Settings Storage Utility** - localStorage management with fallback
3. **Settings Page Component** - Basic form structure and layout

#### Phase 2: Form Implementation
4. **Form Fields** - Input components with validation
5. **Security Features** - Masked API key inputs
6. **Form Actions** - Save and reset functionality

#### Phase 3: Service Integration
7. **Update ChatService** - Read from settings storage
8. **Update EmbeddingService** - Read from settings storage
9. **Navigation Integration** - Add Settings link to main nav

#### Phase 4: Polish & Testing
10. **Error Handling** - Comprehensive validation and error messages
11. **UI Polish** - Consistent styling and responsive design
12. **Testing** - Manual testing of all functionality

### 5.4 Data Model

```typescript
interface LLMSettings {
  reasoning: {
    apiUrl: string;
    apiKey: string;
    model: string;
  };
  embedding: {
    apiUrl: string;
    apiKey: string;
    model: string;
  };
}
```

### 5.5 Default Configuration

```typescript
const DEFAULT_SETTINGS: LLMSettings = {
  reasoning: {
    apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    apiKey: '',
    model: 'deepseek-r1-distill-qwen-32b-250120'
  },
  embedding: {
    apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/embeddings',
    apiKey: '',
    model: 'doubao-embedding-text-240515'
  }
};
```

### 5.6 Storage Strategy

1. **Primary**: Read from localStorage key `llm-settings`
2. **Fallback**: Use environment variables if localStorage is empty
3. **Security**: API keys stored in localStorage (client-side only)
4. **Validation**: URL format validation, required field validation

### 5.7 Backward Compatibility

- Services check localStorage first, then environment variables
- Existing `.env` configuration continues to work
- No breaking changes to existing API
- Gradual migration path for users

## 6. Implementation Priority

**High Priority:**
- Settings storage utility
- Basic Settings page component
- Service integration

**Medium Priority:**
- Form validation
- Security features
- Navigation integration

**Low Priority:**
- UI polish
- Advanced error handling
- Responsive design refinements

This design follows the simplicity-first approach with essential features only, avoiding over-engineering while providing a solid foundation for LLM API configuration management.