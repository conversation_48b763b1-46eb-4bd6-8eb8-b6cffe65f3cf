import { Document, DocumentChunk } from '@/types'
import * as LZString from 'lz-string'

/**
 * Simplified embedding service for document chunking and vector operations
 */

const CHUNK_SIZE = 800
const CHUNK_OVERLAP = 200
const MIN_CHUNK_LENGTH = 50

/**
 * Check if embedding API is properly configured
 */
export function isEmbeddingApiConfigured(): { configured: boolean; message: string } {
  const apiUrl = import.meta.env.VITE_EMBEDDING_API_URL
  const apiKey = import.meta.env.VITE_EMBEDDING_API_KEY
  
  if (!apiUrl) {
    return {
      configured: false,
      message: 'VITE_EMBEDDING_API_URL is not set in your .env file'
    }
  }
  
  if (!apiKey) {
    return {
      configured: false,
      message: 'VITE_EMBEDDING_API_KEY is not set in your .env file'
    }
  }
  
  // Check if API key is a placeholder
  if (apiKey.includes('your-') || apiKey.includes('api-key-here') || apiKey === 'sk-xxx') {
    return {
      configured: false,
      message: 'Please replace the placeholder API key in your .env file with a real OpenAI API key'
    }
  }
  
  return {
    configured: true,
    message: 'Embedding API is properly configured'
  }
}

/**
 * Debug function to test the entire embedding pipeline
 */
export async function debugEmbeddingPipeline(): Promise<{
  success: boolean
  results: Array<{ step: string; status: 'pass' | 'fail' | 'warning'; message: string }>
}> {
  const results: Array<{ step: string; status: 'pass' | 'fail' | 'warning'; message: string }> = []
  let overallSuccess = true

  // Step 1: Check API configuration
  const apiConfig = isEmbeddingApiConfigured()
  results.push({
    step: 'API Configuration',
    status: apiConfig.configured ? 'pass' : 'fail',
    message: apiConfig.message
  })
  if (!apiConfig.configured) overallSuccess = false

  // Step 2: Test document chunking
  try {
    const testText = 'This is a test document for debugging the embedding pipeline. '.repeat(50)
    const chunks = chunkDocument(testText)
    
    if (chunks.length === 0) {
      results.push({
        step: 'Document Chunking',
        status: 'fail',
        message: 'No chunks generated from test document'
      })
      overallSuccess = false
    } else {
      results.push({
        step: 'Document Chunking',
        status: 'pass',
        message: `Generated ${chunks.length} chunks from test document`
      })
    }
  } catch (error) {
    results.push({
      step: 'Document Chunking',
      status: 'fail',
      message: `Chunking failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    })
    overallSuccess = false
  }

  // Step 3: Test embedding generation
  try {
    const testEmbedding = await generateEmbedding('Test text for embedding generation')
    
    if (!testEmbedding.embedding || !Array.isArray(testEmbedding.embedding)) {
      results.push({
        step: 'Embedding Generation',
        status: 'fail',
        message: 'Invalid embedding format returned'
      })
      overallSuccess = false
    } else if (testEmbedding.embedding.length === 1536) {
      // OpenAI text-embedding-ada-002 returns 1536 dimensions
      results.push({
        step: 'Embedding Generation',
        status: 'pass',
        message: `Generated real embedding with ${testEmbedding.embedding.length} dimensions`
      })
    } else if (testEmbedding.embedding.length === 384) {
      // Mock embedding has 384 dimensions
      results.push({
        step: 'Embedding Generation',
        status: 'warning',
        message: `Using mock embedding (${testEmbedding.embedding.length} dimensions) - check API configuration`
      })
    } else {
      results.push({
        step: 'Embedding Generation',
        status: 'warning',
        message: `Generated embedding with ${testEmbedding.embedding.length} dimensions (unexpected size)`
      })
    }
  } catch (error) {
    results.push({
      step: 'Embedding Generation',
      status: 'fail',
      message: `Embedding generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    })
    overallSuccess = false
  }

  // Step 4: Test localStorage operations
  try {
    const testDocumentId = 'debug-test-doc'
    const testChunks: DocumentChunk[] = [
      {
        id: 'chunk-1',
        documentId: testDocumentId,
        documentName: 'Test Document',
        content: 'Test chunk content',
        embedding: [0.1, 0.2, 0.3],
        startIndex: 0,
        endIndex: 18
      }
    ]
    
    // Test storing
    storeEmbeddings(testChunks)
    
    // Test retrieving
    const retrieved = getStoredEmbeddingsForDocument(testDocumentId)
    
    if (retrieved.length === 0) {
      results.push({
        step: 'localStorage Operations',
        status: 'fail',
        message: 'Failed to store or retrieve embeddings from localStorage'
      })
      overallSuccess = false
    } else {
      results.push({
        step: 'localStorage Operations',
        status: 'pass',
        message: `Successfully stored and retrieved ${retrieved.length} chunks`
      })
    }
    
    // Clean up test data
    deleteStoredEmbeddings(testDocumentId)
  } catch (error) {
    results.push({
      step: 'localStorage Operations',
      status: 'fail',
      message: `localStorage operations failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    })
    overallSuccess = false
  }

  // Step 5: Test similarity search
  try {
    const embedding1 = [1, 0, 0]
    const embedding2 = [0.8, 0.6, 0]
    const similarity = cosineSimilarity(embedding1, embedding2)
    
    if (typeof similarity === 'number' && similarity >= 0 && similarity <= 1) {
      results.push({
        step: 'Similarity Calculation',
        status: 'pass',
        message: `Cosine similarity calculation working (test result: ${similarity.toFixed(3)})`
      })
    } else {
      results.push({
        step: 'Similarity Calculation',
        status: 'fail',
        message: 'Invalid similarity calculation result'
      })
      overallSuccess = false
    }
  } catch (error) {
    results.push({
      step: 'Similarity Calculation',
      status: 'fail',
      message: `Similarity calculation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    })
    overallSuccess = false
  }

  return { success: overallSuccess, results }
}
/**
 * Split document content into chunks with overlap
 */
export function chunkDocument(document: Document | string): DocumentChunk[] {
  // Handle both Document object and string input
  const content = typeof document === 'string' ? document : document.content
  const id = typeof document === 'string' ? 'test-doc' : document.id
  const name = typeof document === 'string' ? 'Test Document' : document.name
  const chunks: DocumentChunk[] = []
  const step = CHUNK_SIZE - CHUNK_OVERLAP
  
  for (let i = 0; i < content.length; i += step) {
    const chunkContent = content.slice(i, i + CHUNK_SIZE).trim()
    
    if (chunkContent.length >= MIN_CHUNK_LENGTH) {
      chunks.push({
        id: `${id}-chunk-${chunks.length}`,
        documentId: id,
        documentName: name,
        content: chunkContent,
        startIndex: i,
        endIndex: Math.min(i + CHUNK_SIZE, content.length)
      })
    }
  }
  
  return chunks
}
  
/**
 * Generate embedding for text using embedding API
 */
export async function generateEmbedding(text: string): Promise<{ embedding: number[] }> {
  const apiUrl = import.meta.env.VITE_EMBEDDING_API_URL
  const apiKey = import.meta.env.VITE_EMBEDDING_API_KEY
  const model = import.meta.env.VITE_EMBEDDING_MODEL || 'text-embedding-ada-002'
  
  // Check if API keys are placeholder values
  const isPlaceholderKey = !apiKey || apiKey.includes('your-') || apiKey.includes('api-key-here')
  
  if (!apiUrl || !apiKey || isPlaceholderKey) {
    console.warn('Embedding API not configured properly. Please set VITE_EMBEDDING_API_KEY in your .env file with a real API key.')
    return { embedding: mockEmbedding() }
  }
  
  // Validate input text
  if (!text || typeof text !== 'string' || text.trim().length === 0) {
    console.warn('Invalid text input for embedding, using mock')
    return { embedding: mockEmbedding() }
  }
  
  // Truncate text if too long (most embedding APIs have limits)
  const maxLength = 8000 // Conservative limit
  const truncatedText = text.length > maxLength ? text.substring(0, maxLength) : text
  
  try {
    // Create abort controller for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout
    
    const response = await fetch('/api/embedding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        input: truncatedText,
        model: model
      }),
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error')
      console.error(`Embedding API error: ${response.status} ${response.statusText} - ${errorText}`)
      
      if (response.status === 401) {
        throw new Error('Invalid API key. Please check your VITE_EMBEDDING_API_KEY in .env file.')
      } else if (response.status === 429) {
        throw new Error('API rate limit exceeded. Please try again later.')
      } else {
        throw new Error(`Embedding API error: ${response.status} ${response.statusText}`)
      }
    }
    
    const data = await response.json()
    
    if (!data.data || !data.data[0] || !data.data[0].embedding) {
      console.warn('Invalid embedding response format, using mock')
      return { embedding: mockEmbedding() }
    }
    
    const embedding = data.data[0].embedding
    if (!Array.isArray(embedding) || embedding.length === 0) {
      console.warn('Invalid embedding array, using mock')
      return { embedding: mockEmbedding() }
    }
    
    return { embedding }
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        console.error('Embedding request timeout')
      } else {
        console.error('Error generating embedding:', error.message)
      }
    } else {
      console.error('Unknown error generating embedding:', error)
    }
    
    // Return mock embedding as fallback
    return { embedding: mockEmbedding() }
  }
}
  
/**
 * Generate simple mock embedding for development
 */
function mockEmbedding(): number[] {
  // Simple normalized random embedding (384 dimensions)
  return new Array(384).fill(0).map(() => (Math.random() - 0.5) * 0.1)
}
  
/**
 * Process document and generate embeddings for all chunks
 */
export async function processDocument(document: Document): Promise<void> {
  try {
    const chunks = chunkDocument(document)
    
    if (chunks.length === 0) {
      console.warn('No chunks generated for document:', document.name)
      return
    }
    
    console.log(`Processing ${chunks.length} chunks for document: ${document.name}`)
    
    // Check if API configuration is available
    const apiUrl = import.meta.env.VITE_EMBEDDING_API_URL
    const apiKey = import.meta.env.VITE_EMBEDDING_API_KEY
    
    if (!apiUrl || !apiKey) {
      console.warn('Embedding API not configured, using mock embeddings for development')
      // Use mock embeddings when API is not configured
      for (const chunk of chunks) {
        chunk.embedding = mockEmbedding()
      }
    } else {
      // Process chunks with real API
      const processedChunks: DocumentChunk[] = []
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i]
        try {
          console.log(`Processing chunk ${i + 1}/${chunks.length} for ${document.name}`)
          const result = await generateEmbedding(chunk.content)
          chunk.embedding = result.embedding
          processedChunks.push(chunk)
        } catch (error) {
          console.error(`Failed to generate embedding for chunk ${i + 1}:`, error)
          // Use mock embedding as fallback
          chunk.embedding = mockEmbedding()
          processedChunks.push(chunk)
        }
      }
      
      // Update chunks array with processed chunks
      chunks.splice(0, chunks.length, ...processedChunks)
    }
    
    // Store embeddings with validation
    storeEmbeddings(chunks)
    console.log(`Successfully processed and stored embeddings for document: ${document.name}`)
  } catch (error) {
    console.error('Error processing document:', error)
    throw new Error(`Failed to process document ${document.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
  
/**
 * Calculate cosine similarity between two vectors
 */
export function cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0
    
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0)
    const normA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0))
    const normB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0))
    
    return dotProduct / (normA * normB)
  }
  
/**
 * Check localStorage usage and warn if approaching limits
 */
export function checkStorageUsage(): { used: number; available: number; percentage: number } {
  try {
    let totalUsed = 0
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        totalUsed += localStorage[key].length + key.length
      }
    }
    
    // Estimate available space (most browsers have ~5-10MB limit)
    const estimatedLimit = 5 * 1024 * 1024 // 5MB
    const available = Math.max(0, estimatedLimit - totalUsed)
    const percentage = (totalUsed / estimatedLimit) * 100
    
    if (percentage > 80) {
      console.warn(`localStorage usage is at ${percentage.toFixed(1)}% (${(totalUsed / 1024 / 1024).toFixed(2)}MB)`)
    }
    
    return { used: totalUsed, available, percentage }
  } catch (error) {
    console.error('Error checking storage usage:', error)
    return { used: 0, available: 5 * 1024 * 1024, percentage: 0 }
  }
}

/**
 * Compress data for storage
 */
function compressForStorage(data: any): string {
  try {
    const jsonString = JSON.stringify(data)
    return LZString.compress(jsonString) || jsonString
  } catch (error) {
    console.error('Compression failed, using uncompressed data:', error)
    return JSON.stringify(data)
  }
}

/**
 * Decompress data from storage
 */
function decompressFromStorage(compressedData: string): any {
  try {
    // Try to decompress first
    const decompressed = LZString.decompress(compressedData)
    if (decompressed) {
      return JSON.parse(decompressed)
    }
    // Fallback to direct JSON parsing for backward compatibility
    return JSON.parse(compressedData)
  } catch (error) {
    console.error('Decompression failed:', error)
    throw error
  }
}

/**
 * Store document embeddings with deduplication and compression
 */
export function storeEmbeddings(chunks: DocumentChunk[]): void {
  try {
    // Validate input chunks
    if (!Array.isArray(chunks)) {
      console.error('Invalid chunks data: expected array, got:', typeof chunks)
      return
    }
    
    // Validate each chunk structure
    const validChunks = chunks.filter(chunk => {
      if (!chunk || typeof chunk !== 'object') {
        console.warn('Invalid chunk: not an object', chunk)
        return false
      }
      if (!chunk.id || !chunk.documentId || !chunk.content) {
        console.warn('Invalid chunk: missing required fields', chunk)
        return false
      }
      if (!Array.isArray(chunk.embedding) || chunk.embedding.length === 0) {
        console.warn('Invalid chunk: missing or invalid embedding', chunk.id)
        return false
      }
      return true
    })
    
    if (validChunks.length !== chunks.length) {
      console.warn(`Filtered out ${chunks.length - validChunks.length} invalid chunks`)
    }
    
    if (validChunks.length === 0) {
      console.warn('No valid chunks to store')
      return
    }
    
    // Get existing embeddings and remove chunks for the same documents (deduplication)
    const existingEmbeddings = getStoredEmbeddings()
    const documentIds = new Set(validChunks.map(chunk => chunk.documentId))
    const filteredExisting = existingEmbeddings.filter(chunk => !documentIds.has(chunk.documentId))
    
    // Combine filtered existing with new chunks
    const updatedEmbeddings = [...filteredExisting, ...validChunks]
    
    // Check storage usage before attempting to store
    const storageInfo = checkStorageUsage()
    const testData = compressForStorage(updatedEmbeddings)
    
    if (storageInfo.percentage > 90) {
      console.warn('localStorage is nearly full, attempting selective cleanup')
      // Try to free up space by removing oldest embeddings
       const sortedEmbeddings = updatedEmbeddings.sort((a) => {
         // Prioritize current document chunks
         return documentIds.has(a.documentId) ? 1 : -1
       })
      const reducedEmbeddings = sortedEmbeddings.slice(-Math.floor(sortedEmbeddings.length * 0.8))
      const reducedData = compressForStorage(reducedEmbeddings)
      localStorage.setItem('chatdoc-embeddings', reducedData)
      console.log(`Stored ${validChunks.length} chunks after cleanup (${reducedEmbeddings.length} total)`)
      return
    }
    
    localStorage.setItem('chatdoc-embeddings', testData)
    console.log(`Successfully stored ${validChunks.length} embedding chunks (${updatedEmbeddings.length} total, compressed)`)
  } catch (error) {
    console.error('Error storing embeddings:', error)
    // Improved error recovery with selective cleanup
    if (error instanceof Error && (error.name === 'QuotaExceededError' || error.message.includes('QuotaExceededError'))) {
      console.warn('localStorage quota exceeded, attempting selective cleanup')
      try {
        // Try to keep only the current document's embeddings
        const documentIds = new Set(chunks.map(chunk => chunk.documentId))
        const currentDocumentChunks = chunks.filter(chunk => documentIds.has(chunk.documentId))
        const compressedData = compressForStorage(currentDocumentChunks)
        localStorage.setItem('chatdoc-embeddings', compressedData)
        console.log(`Recovered by storing only current document embeddings (${currentDocumentChunks.length} chunks)`)
      } catch (recoveryError) {
        console.error('Failed to recover from quota error:', recoveryError)
        // Last resort: clear all embeddings
        try {
          localStorage.removeItem('chatdoc-embeddings')
          console.log('Cleared all embeddings as last resort')
        } catch (clearError) {
          console.error('Failed to clear embeddings:', clearError)
        }
      }
    }
  }
}
  
/**
 * Get all stored embeddings with decompression support
 */
export function getStoredEmbeddings(): DocumentChunk[] {
  try {
    const data = localStorage.getItem('chatdoc-embeddings')
    if (!data) {
      return []
    }
    
    const parsed = decompressFromStorage(data)
    
    // Validate the parsed data structure
    if (!Array.isArray(parsed)) {
      console.warn('Stored embeddings data is not an array, resetting')
      localStorage.removeItem('chatdoc-embeddings')
      return []
    }
    
    // Filter out corrupted chunks
    const validChunks = parsed.filter(chunk => {
      if (!chunk || typeof chunk !== 'object') {
        return false
      }
      if (!chunk.id || !chunk.documentId || !chunk.content) {
        return false
      }
      if (!Array.isArray(chunk.embedding)) {
        return false
      }
      return true
    })
    
    // If we filtered out corrupted data, save the cleaned version
    if (validChunks.length !== parsed.length) {
      console.warn(`Cleaned ${parsed.length - validChunks.length} corrupted embedding chunks`)
      try {
        const compressedData = compressForStorage(validChunks)
        localStorage.setItem('chatdoc-embeddings', compressedData)
      } catch (saveError) {
        console.error('Failed to save cleaned embeddings:', saveError)
      }
    }
    
    return validChunks
  } catch (error) {
    console.error('Error loading embeddings, clearing corrupted data:', error)
    try {
      localStorage.removeItem('chatdoc-embeddings')
    } catch (clearError) {
      console.error('Failed to clear corrupted embeddings:', clearError)
    }
    return []
  }
}

/**
 * Get stored embeddings for a specific document
 */
export function getStoredEmbeddingsForDocument(documentId: string): DocumentChunk[] {
  try {
    // Use the unified storage approach instead of document-specific keys
    const allEmbeddings = getStoredEmbeddings()
    return allEmbeddings.filter(chunk => chunk.documentId === documentId)
  } catch (error) {
    console.error('Error retrieving stored embeddings for document:', error)
    return []
  }
}
  
/**
 * Delete stored embeddings for a specific document
 */
export function deleteStoredEmbeddings(documentId: string): void {
  try {
    const allEmbeddings = getStoredEmbeddings()
    const filteredEmbeddings = allEmbeddings.filter(chunk => chunk.documentId !== documentId)
    
    if (filteredEmbeddings.length === 0) {
      // If no embeddings left, remove the key entirely
      localStorage.removeItem('chatdoc-embeddings')
      console.log(`Removed all embeddings (deleted document ${documentId})`)
    } else {
      // Store the filtered embeddings
      const compressedData = compressForStorage(filteredEmbeddings)
      localStorage.setItem('chatdoc-embeddings', compressedData)
      console.log(`Deleted embeddings for document ${documentId} (${allEmbeddings.length - filteredEmbeddings.length} chunks removed)`)
    }
  } catch (error) {
    console.error('Error deleting stored embeddings:', error)
  }
}