import { useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Co<PERSON>, RotateCcw, ThumbsUp, ThumbsDown, MoreHorizontal, ChevronDown, ChevronRight, FileText } from 'lucide-react'
import { ChatMessage, SearchResult } from '@/types'
import { cn } from '@/utils/cn'

interface MessageBubbleProps {
  message: ChatMessage
  onCopy: () => void
  onRefresh: () => void
  onThumbsUp: () => void
  onThumbsDown: () => void
}

export function MessageBubble({ message, onCopy, onRefresh, onThumbsUp, onThumbsDown }: MessageBubbleProps) {
  const [showReasoning, setShowReasoning] = useState(false)
  const [showSearchResults, setShowSearchResults] = useState(false)



  return (
    <div className={`group flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-[80%] rounded-lg px-4 py-3 ${
        message.role === 'user'
          ? 'bg-blue-600 text-white'
          : 'bg-gray-100 text-gray-900 border'
      }`}>
        {/* Message Content */}
        <div className="break-words prose prose-sm max-w-none">
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {message.content}
          </ReactMarkdown>
        </div>
        
        {/* AI Thinking Process */}
        {message.role === 'assistant' && message.reasoning && (
          <div className="mt-3">
            <button
              onClick={() => setShowReasoning(!showReasoning)}
              className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>Thinking<span className="animated-dots"></span></span>
              <ChevronDown className={`w-4 h-4 transition-transform ${showReasoning ? 'rotate-180' : ''}`} />
            </button>
            {showReasoning && (
              <div className="mt-2 bg-gray-50 rounded-md p-3 text-gray-600 text-sm transition-all duration-200 ease-in-out">
                {message.reasoning}
              </div>
            )}
          </div>
        )}

        {/* Search Results Sources */}
        {message.role === 'assistant' && message.searchResults && message.searchResults.length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <button
              onClick={() => setShowSearchResults(!showSearchResults)}
              className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800 transition-colors"
            >
              {showSearchResults ? (
                <ChevronDown className="w-3 h-3" />
              ) : (
                <ChevronRight className="w-3 h-3" />
              )}
              <FileText className="w-3 h-3" />
              <span>Sources ({message.searchResults.length})</span>
            </button>

            {showSearchResults && (
              <div className="mt-2 space-y-2">
                {message.searchResults.map((source: SearchResult, index: number) => (
                  <div key={index} className="bg-gray-50 rounded-md p-3 text-xs">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-700 truncate flex-1 mr-2">
                        {source.chunk.documentName}
                      </span>
                      <span className={cn(
                        'px-2 py-1 rounded-full text-xs font-medium flex-shrink-0',
                        source.similarity >= 0.8 ? 'text-green-600 bg-green-100' :
                        source.similarity >= 0.6 ? 'text-yellow-600 bg-yellow-100' :
                        'text-red-600 bg-red-100'
                      )}>
                        {Math.round(source.similarity * 100)}% match
                      </span>
                    </div>
                    
                    <div className="text-gray-600 leading-relaxed">
                      {source.chunk.content.length > 100 
                        ? `${source.chunk.content.substring(0, 100)}...` 
                        : source.chunk.content
                      }
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}



        {/* Message Action Buttons */}
        {message.role === 'assistant' && (
          <div className="flex items-center gap-1 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={onCopy}
              className="p-1 rounded hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Copy message"
            >
              <Copy className="w-4 h-4" />
            </button>
            <button
              onClick={onRefresh}
              className="p-1 rounded hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Regenerate response"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            <button
              onClick={onThumbsUp}
              className="p-1 rounded hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Good response"
            >
              <ThumbsUp className="w-4 h-4" />
            </button>
            <button
              onClick={onThumbsDown}
              className="p-1 rounded hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="Poor response"
            >
              <ThumbsDown className="w-4 h-4" />
            </button>
            <button
              className="p-1 rounded hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
              title="More options"
            >
              <MoreHorizontal className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}