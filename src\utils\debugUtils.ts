/**
 * Debug utilities for troubleshooting the ChatDoc application
 * These functions can be called from the browser console for debugging
 */

import { getDocuments } from './documentUtils'
import { getStoredEmbeddings } from '../services/embeddingService'

/**
 * Debug function to check document and embedding status
 * Call this in the browser console: window.debugChatDoc()
 */
export function debugChatDoc() {
  console.log('🔍 ChatDoc Debug Information\n');
  
  try {
    // Get all documents
    const documents = getDocuments();
    console.log(`📄 Total Documents: ${documents.length}`);
    
    if (documents.length === 0) {
      console.log('   No documents found. Upload some files or extract web content first.');
      return;
    }
    
    // Analyze documents by type
    const docsByType = documents.reduce((acc, doc) => {
      acc[doc.type] = (acc[doc.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('📊 Documents by type:');
    Object.entries(docsByType).forEach(([type, count]) => {
      console.log(`   ${type}: ${count}`);
    });
    
    // Get all embeddings
    const embeddings = getStoredEmbeddings();
    console.log(`\n🧠 Total Embedding Chunks: ${embeddings.length}`);
    
    if (embeddings.length === 0) {
      console.log('   No embeddings found. This might indicate an issue with document processing.');
      return;
    }
    
    // Analyze embeddings by document
    const embeddingsByDoc = embeddings.reduce((acc, chunk) => {
      acc[chunk.documentId] = (acc[chunk.documentId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n📈 Embeddings by document:');
    documents.forEach(doc => {
      const chunkCount = embeddingsByDoc[doc.id] || 0;
      const status = chunkCount > 0 ? '✅' : '❌';
      console.log(`   ${status} ${doc.name} (${doc.type}): ${chunkCount} chunks`);
      
      if (doc.type === 'web') {
        console.log(`      Source: ${doc.sourceUrl || 'Unknown'}`);
      }
    });
    
    // Check for orphaned embeddings
    const documentIds = new Set(documents.map(doc => doc.id));
    const orphanedEmbeddings = embeddings.filter(chunk => !documentIds.has(chunk.documentId));
    
    if (orphanedEmbeddings.length > 0) {
      console.log(`\n⚠️  Found ${orphanedEmbeddings.length} orphaned embedding chunks`);
      console.log('   These embeddings belong to deleted documents and can be cleaned up.');
    }
    
    // Web document specific analysis
    const webDocs = documents.filter(doc => doc.type === 'web');
    if (webDocs.length > 0) {
      console.log(`\n🌐 Web Documents Analysis:`);
      webDocs.forEach(doc => {
        const chunks = embeddingsByDoc[doc.id] || 0;
        console.log(`   📄 ${doc.name}`);
        console.log(`      URL: ${doc.sourceUrl}`);
        console.log(`      Content Length: ${doc.content?.length || 0} chars`);
        console.log(`      Chunks: ${chunks}`);
        console.log(`      Uploaded: ${new Date(doc.uploadedAt).toLocaleString()}`);
      });
    }
    
    // Storage usage
    const documentsSize = JSON.stringify(documents).length;
    const embeddingsSize = JSON.stringify(embeddings).length;
    console.log(`\n💾 Storage Usage:`);
    console.log(`   Documents: ${(documentsSize / 1024).toFixed(2)} KB`);
    console.log(`   Embeddings: ${(embeddingsSize / 1024).toFixed(2)} KB`);
    console.log(`   Total: ${((documentsSize + embeddingsSize) / 1024).toFixed(2)} KB`);
    
    return {
      documents: documents.length,
      embeddings: embeddings.length,
      webDocuments: webDocs.length,
      documentsWithEmbeddings: Object.keys(embeddingsByDoc).length,
      orphanedEmbeddings: orphanedEmbeddings.length
    };
    
  } catch (error) {
    console.error('❌ Debug function failed:', error);
    return { error: error.message };
  }
}

/**
 * Test similarity search for debugging
 */
export function testSimilaritySearch(query: string = 'test query') {
  console.log(`🎯 Testing similarity search for: "${query}"`);
  
  try {
    const documents = getDocuments();
    const embeddings = getStoredEmbeddings();
    
    if (documents.length === 0 || embeddings.length === 0) {
      console.log('❌ No documents or embeddings available for testing');
      return;
    }
    
    // Mock similarity calculation (since we can't import the full service in debug mode)
    const mockSimilarity = () => Math.random() * 0.8 + 0.1; // Random similarity between 0.1 and 0.9
    
    const results = embeddings
      .map(chunk => ({
        documentName: chunk.documentName,
        similarity: mockSimilarity(),
        content: chunk.content.substring(0, 100) + '...'
      }))
      .filter(result => result.similarity > 0.3)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 5);
    
    console.log(`Found ${results.length} relevant chunks:`);
    results.forEach((result, index) => {
      console.log(`   ${index + 1}. ${result.documentName} (${result.similarity.toFixed(3)})`);
      console.log(`      ${result.content}`);
    });
    
    return results;
    
  } catch (error) {
    console.error('❌ Similarity search test failed:', error);
    return { error: error.message };
  }
}

// Make functions available globally for browser console access
if (typeof window !== 'undefined') {
  (window as any).debugChatDoc = debugChatDoc;
  (window as any).testSimilaritySearch = testSimilaritySearch;
}
