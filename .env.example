# =============================================================================
# API CONFIGURATION - IMPORTANT: Replace placeholder values with real API keys
# =============================================================================

# LLM API Configuration (for chat/reasoning functionality)
VITE_REASONING_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
VITE_REASONING_API_KEY=your_reasoning_api_key_here
VITE_REASONING_MODEL=deepseek-r1-distill-qwen-32b-250120

# Embedding API Configuration (for document processing and RAG)
# CRITICAL: This must be a real API key for document chunking to work properly
# Without a valid key, the system will fall back to mock embeddings
VITE_EMBEDDING_API_URL=https://ark.cn-beijing.volces.com/api/v3/embeddings
VITE_EMBEDDING_API_KEY=your_embedding_api_key_here
VITE_EMBEDDING_MODEL=doubao-embedding-text-240515

# =============================================================================
# ALTERNATIVE EMBEDDING PROVIDERS
# =============================================================================
# Uncomment and configure one of the following if you prefer different providers:

# OpenAI Configuration:
# VITE_EMBEDDING_API_URL=https://api.openai.com/v1/embeddings
# VITE_EMBEDDING_API_KEY=sk-your-openai-api-key-here
# VITE_EMBEDDING_MODEL=text-embedding-ada-002

# Azure OpenAI Configuration:
# VITE_EMBEDDING_API_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment/embeddings?api-version=2023-05-15
# VITE_EMBEDDING_API_KEY=your-azure-openai-key-here
# VITE_EMBEDDING_MODEL=text-embedding-ada-002

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env: cp .env.example .env
# 2. Replace ALL placeholder values (anything containing 'your_' or 'your-') with real API keys
# 3. Ensure your API keys have the necessary permissions for embeddings/chat completions
# 4. Test your configuration by uploading a document - check browser console for errors
# 5. Use the debug function in the app to verify your embedding pipeline is working
#
# TROUBLESHOOTING:
# - If documents aren't being chunked properly, check VITE_EMBEDDING_API_KEY
# - If you see "mock embeddings" warnings, your API key is likely invalid/placeholder
# - Check browser console for detailed error messages
# - Verify your API provider's documentation for correct URL format

# Proxy Configuration for Different Environments
# Development environment settings
VITE_DEV_HOST=localhost
VITE_DEV_PORT=3001

# Production/deployment proxy target (optional)
# VITE_PROXY_TARGET=https://your-production-domain.com

# Vercel deployment (automatically detected)
# VERCEL=1 (set automatically by Vercel)

# Node environment (set automatically)
# NODE_ENV=production