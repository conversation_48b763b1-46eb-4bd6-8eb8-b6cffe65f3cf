# Document Manager Modal Design

## Overview

This design document outlines the transformation of the current `AttachmentModal.tsx` component into a comprehensive document management interface with folder support. The new component will be renamed to better reflect its expanded functionality and will include a simple, flat folder structure for organizing documents.

## Component Renaming

### Current vs. Proposed Names

| Current Name | Proposed Name | Rationale |
|--------------|---------------|-----------|
| `AttachmentModal.tsx` | `DocumentManagerModal.tsx` | More accurately reflects comprehensive document management functionality |
| Alternative Options | `DocumentLibraryModal.tsx` | Emphasizes the organizational aspect |
| | `DocumentWorkspaceModal.tsx` | Suggests a working environment for documents |

**Recommended**: `DocumentManagerModal.tsx` - Most descriptive of actual functionality (upload, organize, select, delete documents).

### Impact Analysis

```mermaid
graph TD
    A[AttachmentModal.tsx] --> B[DocumentManagerModal.tsx]
    
    subgraph "Import Updates Required"
        C[MainChatInterface.tsx]
        D[Other components using AttachmentModal]
    end
    
    subgraph "Export Updates"
        E[Component export name]
        F[File name in components/index.ts]
    end
    
    B --> C
    B --> D
    B --> E
    B --> F
```

## Technology Stack & Dependencies

### Recommended Library: React-Folder-Tree

**Why this choice:**
- Simple API that matches our basic needs
- TypeScript support included
- Compatible with existing Lucide React icons
- Lightweight implementation

**Installation:**
```bash
npm install react-folder-tree
```

## Architecture Overview

### Data Structure Changes

**Extended Document Type:**
```typescript
interface Document {
  id: string
  name: string
  type: 'txt' | 'md' | 'web'
  size: number
  content: string
  uploadedAt: Date
  sourceUrl?: string
  excerpt?: string
  folderId?: string  // New: Optional folder assignment
}
```

**New Folder Type:**
```typescript
interface Folder {
  id: string
  name: string
  createdAt: Date
}
```

### Storage Extension

Add basic folder methods to existing `documentStorage`:

```typescript
export const documentStorage = {
  // ... existing methods
  
  getFolders(): Folder[],
  saveFolder(folder: Folder): void,
  deleteFolder(id: string): void,
  getDocumentsByFolder(folderId?: string): Document[]
}
```

## Component Architecture

### Tab Structure

Add a new "Folders" tab as the first tab:

1. **Folders Tab** (NEW) - Manage folders and view documents by folder
2. **Upload Files Tab** (existing)
3. **Extract from URL Tab** (existing)

### Basic Folder Tab Layout

```
┌─────────────────────────────────────────────────────────┐
│ [Folders] [Upload Files] [Extract from URL]             │
├─────────────────────────────────────────────────────────┤
│ Folder List:                                            │
│ • All Documents (default)                               │
│ • Research                                              │
│ • Projects                                              │
│ [+ New Folder]                                          │
│                                                         │
│ Documents in selected folder displayed below            │
└─────────────────────────────────────────────────────────┘
```

## User Interface Design

### Simple Folder Management

**Basic Features:**
- List folders with document counts
- Create new folder (simple name input)
- Delete folder (moves documents to "All Documents")
- Select folder to filter document view

**Folder Tab Implementation:**
```typescript
const FoldersTab = () => {
  const [folders, setFolders] = useState([])
  const [selectedFolder, setSelectedFolder] = useState('all')
  const [newFolderName, setNewFolderName] = useState('')
  
  return (
    <div className="p-6">
      {/* Simple folder list */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Folders</h3>
        {folders.map(folder => (
          <div key={folder.id} className="flex items-center justify-between p-2">
            <button 
              onClick={() => setSelectedFolder(folder.id)}
              className={selectedFolder === folder.id ? 'font-bold' : ''}
            >
              📁 {folder.name} ({folder.documentCount})
            </button>
            <button onClick={() => deleteFolder(folder.id)}>🗑️</button>
          </div>
        ))}
      </div>
      
      {/* Simple new folder input */}
      <div className="flex gap-2">
        <input 
          value={newFolderName}
          onChange={(e) => setNewFolderName(e.target.value)}
          placeholder="New folder name"
          className="border rounded px-2 py-1"
        />
        <button onClick={() => createFolder(newFolderName)}>Create</button>
      </div>
    </div>
  )
}
```

## Implementation Plan

### Step 1: Rename Component

```typescript
// Rename file: AttachmentModal.tsx → DocumentManagerModal.tsx
// Update component name and exports

export default function DocumentManagerModal({
  isOpen,
  onClose,
  documents,
  selectedDocumentIds,
  onSelectionChange,
  onDocumentsUpdate
}: DocumentManagerModalProps) {
  // ... existing logic
}
```

### Step 2: Add Folder Data Types

```typescript
// types/index.ts
export interface Folder {
  id: string
  name: string
  createdAt: Date
}

// Extend existing Document interface
export interface Document {
  // ... existing properties
  folderId?: string  // Add optional folder reference
}
```

### Step 3: Extend Storage Functions

```typescript
// utils/index.ts - Add to documentStorage
export const documentStorage = {
  // ... existing methods
  
  getFolders(): Folder[] {
    return StorageService.get<Folder[]>('chatdoc_folders') || [
      { id: 'all', name: 'All Documents', createdAt: new Date() }
    ]
  },

  saveFolder(folder: Folder): void {
    StorageService.update<Folder[]>('chatdoc_folders', (folders) => {
      const updated = (folders || []).filter(f => f.id !== folder.id)
      updated.push(folder)
      return updated
    })
  },

  deleteFolder(id: string): void {
    // Don't delete 'all' folder
    if (id === 'all') return
    
    StorageService.update<Folder[]>('chatdoc_folders', (folders) => {
      return (folders || []).filter(f => f.id !== id)
    })
  },

  getDocumentsByFolder(folderId?: string): Document[] {
    const documents = this.getDocuments()
    if (!folderId || folderId === 'all') {
      return documents
    }
    return documents.filter(d => d.folderId === folderId)
  }
}
```

### Step 4: Add Folders Tab

```typescript
// Add to DocumentManagerModal component
const [activeTab, setActiveTab] = useState<'folders' | 'files' | 'url'>('folders')
const [folders, setFolders] = useState<Folder[]>([])
const [selectedFolderId, setSelectedFolderId] = useState<string>('all')

// Load folders on mount
useEffect(() => {
  setFolders(documentStorage.getFolders())
}, [])

// Add folders tab button
<button
  onClick={() => setActiveTab('folders')}
  className={`flex items-center gap-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
    activeTab === 'folders'
      ? 'border-blue-500 text-blue-600'
      : 'border-transparent text-gray-500 hover:text-gray-700'
  }`}
>
  <FolderIcon className="w-4 h-4" />
  Folders
</button>
```

## Implementation Approach

Simple, direct implementation:

1. Rename component file and update imports
2. Add basic folder data types to existing interfaces
3. Extend documentStorage with folder methods
4. Add folders tab to existing tab structure
5. Test basic functionality

## Testing Strategy

### Basic Tests

**Storage Tests:**
- Test folder creation, deletion, and retrieval
- Test document-folder assignment
- Test default folder behavior

**Component Tests:**
- Test folders tab renders correctly
- Test folder list displays
- Test new folder creation flow
- Test document filtering by folder

