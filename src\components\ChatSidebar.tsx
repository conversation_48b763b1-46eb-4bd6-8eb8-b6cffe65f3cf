import { useState } from 'react'
import {
  MessageCircle,
  Plus,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  MessageSquare,
  Star,
  Trash2,
  Info,
  FileText,
  Edit2
} from 'lucide-react'
import { ChatSession, Document } from '@/types'
import { formatDistanceToNow } from 'date-fns'
import SessionRenameInput from './SessionRenameInput'
import { renameSession as renameSessionService } from '@/services/chatService'

interface ChatSidebarProps {
  sessions: ChatSession[]
  currentSession: ChatSession | null
  onSessionSelect: (session: ChatSession) => void
  onNewChat: () => void
  onDeleteSession: (sessionId: string) => void
  onToggleFavorite: (sessionId: string) => void
  onRenameSession: (sessionId: string, newTitle: string) => void
  documents: Document[]
  selectedDocumentIds: string[]
  onDocumentSelectionChange: (documentIds: string[]) => void
  isExpanded: boolean
  onToggleExpanded: () => void
}

export default function ChatSidebar({
  sessions,
  currentSession,
  onSessionSelect,
  onNewChat,
  onDeleteSession,
  onToggleFavorite,
  onRenameSession,
  documents,
  selectedDocumentIds,
  onDocumentSelectionChange,
  isExpanded,
  onToggleExpanded
}: ChatSidebarProps) {


  const [chatHistoryExpanded, setChatHistoryExpanded] = useState(true)
  const [favoritesExpanded, setFavoritesExpanded] = useState(false)
  const [documentsExpanded, setDocumentsExpanded] = useState(true)
  const [renamingSessionId, setRenamingSessionId] = useState<string | null>(null)



  const favoritesSessions = sessions.filter(session => session.isFavorite)
  const recentSessions = sessions.filter(session => !session.isFavorite).slice(0, 10)

  const handleSessionClick = (session: ChatSession) => {
    onSessionSelect(session)
    // Auto-collapse sidebar on mobile after selection
    const isMobile = window.innerWidth < 768
    if (isMobile && isExpanded) {
      onToggleExpanded()
    }
  }

  const handleRenameSession = (sessionId: string, newTitle: string): boolean => {
    const success = renameSessionService(sessionId, newTitle)
    if (success) {
      onRenameSession(sessionId, newTitle)
      setRenamingSessionId(null)
    }
    return success
  }

  return (
    <>
      {/* Sidebar */}
      <div className={`
        ${isExpanded ? 'w-80' : 'w-16'} 
        bg-gray-50 border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out
        md:relative
        ${isExpanded ? 'fixed inset-y-0 left-0 z-50 md:relative' : ''}
      `}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200 relative">
          {isExpanded ? (
            <>
              <div className="flex items-center gap-3 mb-4">
                <MessageCircle className="w-6 h-6 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">ChatDoc</span>
              </div>
              
              <button
                onClick={onNewChat}
                className="w-full flex items-center gap-3 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span className="text-sm font-medium">New Chat</span>
                <span className="ml-auto text-xs bg-blue-500 px-2 py-1 rounded">Ctrl+K</span>
              </button>
            </>
          ) : (
            <div className="flex flex-col items-center gap-3">
              <div 
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors"
                title="ChatDoc"
              >
                <MessageCircle className="w-6 h-6 text-blue-600" />
              </div>
              
              <button
                onClick={onNewChat}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                title="New Chat (Ctrl+K)"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
          )}
          
          {/* Toggle Button */}
          <button
            onClick={onToggleExpanded}
            className="absolute -right-3 top-4 w-6 h-6 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors shadow-sm"
          >
            {isExpanded ? (
              <ChevronLeft className="w-3 h-3 text-gray-600" />
            ) : (
              <ChevronRight className="w-3 h-3 text-gray-600" />
            )}
          </button>
        </div>

        {/* Navigation Sections */}
        <div className="flex-1 overflow-y-auto">
          {/* Chat History Section */}
          <div className="p-2">
            {isExpanded ? (
              <>
                <button
                  onClick={() => setChatHistoryExpanded(!chatHistoryExpanded)}
                  className="w-full flex items-center gap-2 px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronDown className={`w-4 h-4 transition-transform ${chatHistoryExpanded ? '' : '-rotate-90'}`} />
                  <MessageSquare className="w-4 h-4" />
                  <span>Chat History</span>
                </button>
                
                {chatHistoryExpanded && (
                  <div className="mt-2 space-y-1">
                    {recentSessions.length === 0 ? (
                      <p className="px-4 py-2 text-sm text-gray-500">No conversations yet</p>
                    ) : (
                      recentSessions.map((session) => (
                        <div key={session.id}>
                          {renamingSessionId === session.id ? (
                            <div className="px-3 py-2">
                              <SessionRenameInput
                                initialTitle={session.title}
                                onSave={(newTitle) => handleRenameSession(session.id, newTitle)}
                                onCancel={() => setRenamingSessionId(null)}
                                className="text-sm"
                              />
                            </div>
                          ) : (
                            <div
                              className={`group flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-colors ${
                                currentSession?.id === session.id
                                  ? 'bg-blue-100 text-blue-900'
                                  : 'hover:bg-gray-100 text-gray-700'
                              }`}
                              onClick={() => handleSessionClick(session)}
                            >
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{session.title}</p>
                                <p className="text-xs text-gray-500">
                                  {formatDistanceToNow(new Date(session.updatedAt), { addSuffix: true })}
                                </p>
                              </div>
                              
                              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setRenamingSessionId(session.id)
                                  }}
                                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                                  title="Rename chat"
                                >
                                  <Edit2 className="w-3 h-3 text-gray-400" />
                                </button>
                                
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onToggleFavorite(session.id)
                                  }}
                                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                                  title={session.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                                >
                                  <Star className={`w-3 h-3 ${session.isFavorite ? 'fill-yellow-400 text-yellow-400' : 'text-gray-400'}`} />
                                </button>
                                
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onDeleteSession(session.id)
                                  }}
                                  className="p-1 hover:bg-red-100 text-red-600 rounded transition-colors"
                                  title="Delete chat"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                )}
              </>
            ) : (
              <div 
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors"
                title="Chat History"
                onClick={() => {
                  onToggleExpanded()
                  setChatHistoryExpanded(true)
                }}
              >
                <MessageSquare className="w-5 h-5 text-gray-600 mx-auto" />
              </div>
            )}
          </div>

          {/* Favorites Section */}
          <div className="p-2">
            {isExpanded ? (
              <>
                <button
                  onClick={() => setFavoritesExpanded(!favoritesExpanded)}
                  className="w-full flex items-center gap-2 px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronDown className={`w-4 h-4 transition-transform ${favoritesExpanded ? '' : '-rotate-90'}`} />
                  <Star className="w-4 h-4" />
                  <span>Favorites</span>
                  {favoritesSessions.length > 0 && (
                    <span className="ml-auto bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
                      {favoritesSessions.length}
                    </span>
                  )}
                </button>
                
                {favoritesExpanded && (
                  <div className="mt-2 space-y-1">
                    {favoritesSessions.length === 0 ? (
                      <p className="px-4 py-2 text-sm text-gray-500">No favorites yet</p>
                    ) : (
                      favoritesSessions.map((session) => (
                        <div key={session.id}>
                          {renamingSessionId === session.id ? (
                            <div className="px-3 py-2">
                              <SessionRenameInput
                                initialTitle={session.title}
                                onSave={(newTitle) => handleRenameSession(session.id, newTitle)}
                                onCancel={() => setRenamingSessionId(null)}
                                className="text-sm"
                              />
                            </div>
                          ) : (
                            <div
                              className={`group flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-colors ${
                                currentSession?.id === session.id
                                  ? 'bg-blue-100 text-blue-900'
                                  : 'hover:bg-gray-100 text-gray-700'
                              }`}
                              onClick={() => handleSessionClick(session)}
                            >
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 flex-shrink-0" />
                              
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{session.title}</p>
                                <p className="text-xs text-gray-500">
                                  {formatDistanceToNow(new Date(session.updatedAt), { addSuffix: true })}
                                </p>
                              </div>
                              
                              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setRenamingSessionId(session.id)
                                  }}
                                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                                  title="Rename chat"
                                >
                                  <Edit2 className="w-3 h-3 text-gray-400" />
                                </button>
                                
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onToggleFavorite(session.id)
                                  }}
                                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                                  title="Remove from favorites"
                                >
                                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                </button>
                                
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onDeleteSession(session.id)
                                  }}
                                  className="p-1 hover:bg-red-100 text-red-600 rounded transition-colors"
                                  title="Delete chat"
                                >
                                  <Trash2 className="w-3 h-3" />
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                )}
              </>
            ) : (
              <div 
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors relative"
                title={`Favorites (${favoritesSessions.length})`}
                onClick={() => {
                  onToggleExpanded()
                  setFavoritesExpanded(true)
                }}
              >
                <Star className="w-5 h-5 text-gray-600 mx-auto" />
                {favoritesSessions.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-yellow-400 text-yellow-900 text-xs w-5 h-5 rounded-full flex items-center justify-center font-medium">
                    {favoritesSessions.length}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Documents Section */}
          <div className="p-2">
            {isExpanded ? (
              <>
                <button
                  onClick={() => setDocumentsExpanded(!documentsExpanded)}
                  className="w-full flex items-center gap-2 px-2 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronDown className={`w-4 h-4 transition-transform ${documentsExpanded ? '' : '-rotate-90'}`} />
                  <FileText className="w-4 h-4" />
                  <span>Documents</span>
                  <div className="ml-auto flex items-center gap-1">
                    {documents.length > 0 && (
                      <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
                        {documents.length}
                      </span>
                    )}
                    {selectedDocumentIds.length > 0 && (
                      <span className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">
                        {selectedDocumentIds.length} sel
                      </span>
                    )}
                  </div>
                </button>
                
                {documentsExpanded && (
                  <div className="mt-2 space-y-1">
                    {documents.length === 0 ? (
                      <p className="px-4 py-2 text-sm text-gray-500">No documents uploaded</p>
                    ) : (
                      documents.slice(0, 5).map((document) => (
                        <div
                          key={document.id}
                          className={`flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-colors ${
                            selectedDocumentIds.includes(document.id)
                              ? 'bg-blue-100 text-blue-900'
                              : 'hover:bg-gray-100 text-gray-700'
                          }`}
                          onClick={() => {
                            const newSelection = selectedDocumentIds.includes(document.id)
                              ? selectedDocumentIds.filter(id => id !== document.id)
                              : [...selectedDocumentIds, document.id]
                            console.log('ChatSidebar: Document clicked:', {
                              documentId: document.id,
                              documentName: document.name,
                              wasSelected: selectedDocumentIds.includes(document.id),
                              newSelection,
                              newCount: newSelection.length
                            })
                            onDocumentSelectionChange(newSelection)
                          }}
                        >
                          <FileText className="w-4 h-4 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{document.name}</p>
                            <p className="text-xs text-gray-500 truncate">{document.type}</p>
                          </div>
                        </div>
                      ))
                    )}
                    
                    {documents.length > 5 && (
                      <p className="px-4 py-2 text-xs text-gray-500">
                        +{documents.length - 5} more documents
                      </p>
                    )}
                  </div>
                )}
              </>
            ) : (
              <div 
                className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors relative"
                title={`Documents (${documents.length} total, ${selectedDocumentIds.length} selected)`}
                onClick={() => {
                  onToggleExpanded()
                  setDocumentsExpanded(true)
                }}
              >
                <FileText className="w-5 h-5 text-gray-600 mx-auto" />
                {selectedDocumentIds.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-medium">
                    {selectedDocumentIds.length}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          {isExpanded ? (
            <a
              href="#"
              className="flex items-center gap-2 px-2 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Info className="w-4 h-4" />
              <span>About ChatDoc</span>
            </a>
          ) : (
            <div 
              className="p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-colors"
              title="About ChatDoc"
            >
              <Info className="w-4 h-4 text-gray-500 mx-auto" />
            </div>
          )}
        </div>
      </div>
    </>
  )
}