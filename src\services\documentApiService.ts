import { Document, UploadResult } from '@/types'

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

// Helper function for API requests
async function request<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  if (!response.ok) {
    throw new Error(`API request failed: ${response.statusText}`)
  }

  return response.json()
}

/**
 * Upload a document to the server
 */
export async function uploadDocument(file: File): Promise<UploadResult> {
  const formData = new FormData()
  formData.append('document', file)

  const response = await fetch(`${API_BASE_URL}/documents/upload`, {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`)
  }

  return response.json()
}

/**
 * Get all documents for the current user
 */
export async function getAllDocuments(): Promise<Document[]> {
  return request<Document[]>('/documents')
}

/**
 * Get a specific document by ID
 */
export async function getDocument(id: string): Promise<Document> {
  return request<Document>(`/documents/${id}`)
}

/**
 * Delete a document
 */
export async function deleteDocument(id: string): Promise<void> {
  await request(`/documents/${id}`, {
    method: 'DELETE',
  })
}

/**
 * Update document metadata
 */
export async function updateDocument(
  id: string,
  updates: Partial<Pick<Document, 'name'>>
): Promise<Document> {
  return request<Document>(`/documents/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(updates),
  })
}