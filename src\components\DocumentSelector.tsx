import { Check, FileText } from 'lucide-react'
import { Document } from '@/types'
import { cn } from '@/utils/cn'

export function DocumentSelector({ 
  documents,
  selectedDocumentIds, 
  onSelectionChange, 
  disabled = false,
  className 
}: {
  documents: Document[]
  selectedDocumentIds: string[]
  onSelectionChange: (documentIds: string[]) => void
  disabled?: boolean
  className?: string
}) {

  const handleDocumentToggle = (documentId: string) => {
    if (disabled) return
    
    const newSelection = selectedDocumentIds.includes(documentId)
      ? selectedDocumentIds.filter(id => id !== documentId)
      : [...selectedDocumentIds, documentId]
    
    onSelectionChange(newSelection)
  }

  const selectedCount = selectedDocumentIds.length
  const totalCount = documents.length

  if (documents.length === 0) {
    return (
      <div className={cn(
        "bg-yellow-50 border border-yellow-200 rounded-lg p-4",
        className
      )}>
        <div className="flex items-center text-yellow-800">
          <FileText className="h-5 w-5 mr-2" />
          <span className="text-sm font-medium">
            No documents available. Please upload documents first.
          </span>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("border border-gray-200 rounded-lg", className)}>
      {/* Header */}
      <div className="flex items-center p-3 border-b border-gray-200">
        <FileText className="h-5 w-5 text-gray-600 mr-2" />
        <span className="font-medium text-gray-900">
          Documents ({selectedCount}/{totalCount} selected)
        </span>
      </div>

      {/* Individual Documents */}
      {documents.map((document) => {
        const isSelected = selectedDocumentIds.includes(document.id)
        
        return (
          <div
            key={document.id}
            className={cn(
              "flex items-center p-3 hover:bg-gray-50 cursor-pointer",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            onClick={() => handleDocumentToggle(document.id)}
          >
            <div className={cn(
              "flex items-center justify-center w-4 h-4 border-2 rounded mr-3",
              isSelected ? "bg-blue-600 border-blue-600" : "border-gray-300"
            )}>
              {isSelected && <Check className="h-3 w-3 text-white" />}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <FileText className="h-4 w-4 text-gray-500 mr-2 flex-shrink-0" />
                <span className="text-sm font-medium text-gray-900 truncate">
                  {document.name}
                </span>
              </div>
              <div className="flex items-center mt-1 text-xs text-gray-500">
                <span>{document.type.toUpperCase()}</span>
                <span className="mx-1">•</span>
                <span>{(document.size / 1024).toFixed(1)} KB</span>
                <span className="mx-1">•</span>
                <span>{new Date(document.uploadedAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}