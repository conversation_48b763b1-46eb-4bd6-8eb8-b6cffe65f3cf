# Web Content Extraction Feature - Technical Design Document

## 1. Product Overview
Implement a web content extraction feature that allows users to input URLs and extract clean, readable content for document chat. The feature uses browser-compatible libraries to extract and convert web content into LLM-friendly Markdown format, integrating seamlessly with the existing document management system.

## 2. Core Features

### 2.1 Feature Module
Our web content extraction feature consists of the following main components:
1. **URL Input Interface**: Enhanced document upload area with URL input field and extraction controls
2. **Content Extraction Service**: Browser-based service using @mozilla/readability for clean content extraction
3. **Markdown Conversion**: HTML-to-Markdown conversion using dom-to-semantic-markdown for LLM compatibility
4. **Document Integration**: Extended document storage system supporting 'web' type documents
5. **Error Handling**: Comprehensive error management for CORS, network, and extraction failures

### 2.2 Page Details

| Page Name | Module Name | Feature Description |
|-----------|-------------|--------------------|
| Documents Page | URL Input Section | Add URL input field, extract button, loading states, and error display alongside existing file upload |
| Documents Page | Web Document Display | Show extracted web documents with source URL, extraction date, and content preview |
| Chat Page | Document Selection | Enable selection of web-extracted documents for RAG chat sessions |

## 3. Core Process

### 3.1 User Flow
1. User navigates to Documents page
2. User enters URL in the new URL input field
3. User clicks "Extract Content" button
4. System fetches webpage content and extracts readable text
5. System converts HTML to Markdown format
6. System saves as new document with type 'web'
7. User can select web document for chat sessions

### 3.2 Process Flow
```mermaid
graph TD
    A[Documents Page] --> B[URL Input]
    B --> C[Content Extraction]
    C --> D[Markdown Conversion]
    D --> E[Document Storage]
    E --> F[Chat Integration]
    C --> G[Error Handling]
    G --> B
```

## 4. Technical Architecture

### 4.1 Architecture Design
```mermaid
graph TD
    A[User Browser] --> B[DocumentsPage Component]
    B --> C[WebExtractionService]
    C --> D[@mozilla/readability]
    C --> E[dom-to-semantic-markdown]
    C --> F[Browser Fetch API]
    F --> G[Target Website]
    C --> H[Document Storage]
    H --> I[LocalStorage]
    
    subgraph "Frontend Layer"
        B
        C
    end
    
    subgraph "Browser APIs"
        D
        E
        F
    end
    
    subgraph "Storage Layer"
        H
        I
    end
```

### 4.2 Technology Stack
- Frontend: React@18 + TypeScript + Tailwind CSS
- Content Extraction: @mozilla/readability
- Markdown Conversion: dom-to-semantic-markdown
- Storage: Existing localStorage-based document storage
- Network: Browser Fetch API with CORS handling

## 5. Component Design

### 5.1 WebExtractionService
```typescript
interface WebExtractionService {
  extractContent(url: string): Promise<ExtractedContent>
  validateUrl(url: string): boolean
  handleCorsProxy(url: string): string
}

interface ExtractedContent {
  title: string
  content: string
  excerpt: string
  byline?: string
  sourceUrl: string
}
```

### 5.2 Enhanced Document Type
```typescript
interface Document {
  id: string
  name: string
  content: string
  uploadedAt: Date
  type: 'file' | 'web'  // Extended type
  sourceUrl?: string    // New field for web documents
  excerpt?: string      // New field for content preview
}
```

### 5.3 URL Input Component
```typescript
interface UrlInputProps {
  onExtractComplete: (document: Document) => void
  isExtracting: boolean
  error?: string
}
```

## 6. Implementation Tasks

### Phase 1: Dependencies and Types (Task 1)
- [ ] Add @mozilla/readability dependency to package.json
- [ ] Add dom-to-semantic-markdown dependency to package.json
- [ ] Extend Document interface in src/types/index.ts
- [ ] Add web extraction types and interfaces

**Acceptance Criteria**: Dependencies installed, types extended, no TypeScript errors

### Phase 2: Web Extraction Service (Task 2)
- [ ] Create src/services/webExtractionService.ts
- [ ] Implement URL validation function
- [ ] Implement content extraction using @mozilla/readability
- [ ] Implement HTML-to-Markdown conversion
- [ ] Add basic error handling for network failures

**Acceptance Criteria**: Service extracts content from valid URLs, converts to Markdown, handles basic errors

### Phase 3: CORS Handling (Task 3)
- [ ] Implement CORS proxy detection
- [ ] Add fallback strategies for CORS-blocked requests
- [ ] Implement user feedback for CORS limitations
- [ ] Add retry mechanisms for failed requests

**Acceptance Criteria**: Service handles CORS errors gracefully, provides user feedback

### Phase 4: URL Input UI (Task 4)
- [ ] Create UrlInput component in src/components/
- [ ] Add URL input field with validation
- [ ] Implement loading states during extraction
- [ ] Add error display and user feedback
- [ ] Style component with Tailwind CSS

**Acceptance Criteria**: URL input component renders, validates URLs, shows loading/error states

### Phase 5: DocumentsPage Integration (Task 5)
- [ ] Integrate UrlInput component into DocumentsPage
- [ ] Update document list to display web documents
- [ ] Add source URL display for web documents
- [ ] Implement extraction completion handling
- [ ] Update pagination to include web documents

**Acceptance Criteria**: URL input appears on Documents page, extracted content saves as documents

### Phase 6: Storage Integration (Task 6)
- [ ] Extend document storage utilities in src/utils/index.ts
- [ ] Update saveDocument function to handle web documents
- [ ] Implement web document validation
- [ ] Add source URL indexing for search

**Acceptance Criteria**: Web documents save/load correctly, integrate with existing storage

### Phase 7: Chat Integration (Task 7)
- [ ] Verify web documents work with DocumentSelector
- [ ] Test web content in RAG chat sessions
- [ ] Ensure embedding pipeline processes web content
- [ ] Add web document indicators in chat interface

**Acceptance Criteria**: Web documents selectable for chat, work with existing RAG functionality

### Phase 8: Error Handling & Polish (Task 8)
- [ ] Implement comprehensive error handling
- [ ] Add user-friendly error messages
- [ ] Implement extraction timeout handling
- [ ] Add content length validation
- [ ] Polish UI/UX consistency

**Acceptance Criteria**: Robust error handling, consistent user experience

## 7. Error Handling Strategy

### 7.1 Error Types
- **Network Errors**: Connection failures, timeouts
- **CORS Errors**: Cross-origin request blocking
- **Extraction Errors**: Content parsing failures
- **Validation Errors**: Invalid URLs, unsupported content

### 7.2 Error Handling Approach
- Graceful degradation with user feedback
- Retry mechanisms for transient failures
- Clear error messages with suggested actions
- Fallback strategies for CORS limitations

## 8. Integration Points

### 8.1 Existing Systems
- **Document Storage**: Extend existing localStorage-based storage
- **RAG Chat**: Ensure compatibility with embedding pipeline
- **UI Components**: Maintain consistency with existing Tailwind patterns
- **Error Handling**: Follow existing error handling patterns

### 8.2 Data Flow
```mermaid
sequenceDiagram
    participant U as User
    participant UI as DocumentsPage
    participant WS as WebExtractionService
    participant S as Storage
    participant C as ChatPage
    
    U->>UI: Enter URL
    UI->>WS: Extract content
    WS->>WS: Fetch & parse
    WS->>WS: Convert to Markdown
    WS->>UI: Return extracted content
    UI->>S: Save as web document
    S->>UI: Confirm save
    UI->>U: Show success
    U->>C: Select for chat
    C->>S: Load web document
```

## 9. Testing Strategy

### 9.1 Unit Tests
- URL validation functions
- Content extraction logic
- Markdown conversion
- Error handling scenarios

### 9.2 Integration Tests
- End-to-end extraction workflow
- Document storage integration
- Chat system compatibility
- UI component interactions

### 9.3 Manual Testing
- Various website types and structures
- CORS scenarios and limitations
- Error conditions and recovery
- User experience flows

This implementation follows the agile methodology with small, testable tasks that build incrementally toward a complete web content extraction feature.