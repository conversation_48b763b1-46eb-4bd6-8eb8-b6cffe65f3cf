# ChatDoc UI Redesign - Product Requirements Document

## 1. Product Overview

Redesign ChatDoc from a multi-page application into a simplified, single-page chat interface that prioritizes user experience and streamlines document-based conversations. The new design eliminates navigation complexity while maintaining all core RAG functionality through an integrated attachment system.

- **Primary Goal:** Transform ChatDoc into a modern, chat-first application similar to popular messaging platforms
- **Target Users:** Knowledge workers, researchers, and professionals who need to interact with their documents through conversational AI
- **Market Value:** Reduce cognitive load and improve user engagement by focusing on the core chat experience

## 2. Core Features

### 2.1 User Roles

No role distinction required - all users have the same access level and functionality.

### 2.2 Feature Module

Our redesigned ChatDoc application consists of a single main interface:

1. **Main Chat Interface**: Left sidebar with chat history and favorites, main chat area with full-height message display, enhanced input with attachment functionality.

### 2.3 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| Main Chat Interface | Left Sidebar | Display chat history with session management, favorites section for starred conversations, new chat creation |
| Main Chat Interface | Chat Display Area | Full-height scrollable message display, message bubbles with user/AI distinction, AI reasoning toggle, source citations |
| Main Chat Interface | Enhanced Input | Multi-line text input, attachment icon for document management, send button, document tags display |
| Main Chat Interface | Attachment Modal | File upload with drag & drop, URL input for web extraction, document list management, add/remove documents from chat context |

## 3. Core Process

**Main User Flow:**
1. User opens ChatDoc and sees the main chat interface
2. User can start a new chat or select from chat history
3. User clicks attachment icon to manage documents for the current chat
4. User types message and sends to AI
5. AI responds with contextual information from selected documents
6. User can favorite important conversations for quick access

```mermaid
graph TD
    A[Main Chat Interface] --> B[Select/Create Chat Session]
    B --> C[Manage Documents via Attachment Modal]
    C --> D[Send Message]
    D --> E[AI Response with RAG]
    E --> F[Continue Conversation]
    F --> D
    B --> G[Access Chat History]
    B --> H[Access Favorites]
```

## 4. User Interface Design

### 4.1 Design Style

- **Primary Colors:** Blue (#3B82F6) for primary actions, Gray (#6B7280) for secondary elements
- **Secondary Colors:** Light gray (#F3F4F6) for backgrounds, White (#FFFFFF) for content areas
- **Button Style:** Rounded corners (rounded-lg), subtle shadows, hover states with color transitions
- **Font:** System fonts with text-sm (14px) for body text, text-xl (20px) for headings
- **Layout Style:** Clean sidebar layout with card-based message bubbles, minimal borders
- **Icons:** Lucide React icons for consistency, 16px (w-4 h-4) for most UI elements

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| Main Chat Interface | Left Sidebar | Width: 320px (w-80), Background: Light gray (#F9FAFB), Border: Right border, Sections: New Chat button, Chat History list, Favorites list |
| Main Chat Interface | Chat Display Area | Background: White, Full height with overflow scroll, Message bubbles with max-width 70%, User messages: Blue background, AI messages: Gray background |
| Main Chat Interface | Enhanced Input | Fixed bottom position, White background, Border top, Multi-line textarea, Attachment icon (bottom-left), Send button (bottom-right), Document tags above input |
| Main Chat Interface | Attachment Modal | Centered overlay, White background, Rounded corners, Shadow, Compact size (max-width: 500px), Sections: File upload area, URL input, Document list |

### 4.3 Responsiveness

Desktop-first design with mobile-adaptive sidebar that collapses on smaller screens. Touch interaction optimization for mobile devices with larger touch targets and swipe gestures for sidebar navigation.
