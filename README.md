# ChatDoc - AI Document Chat Assistant

A modern React application that allows users to upload documents and chat with them using AI. Built with TypeScript, Vite, and Tailwind CSS.

## Features

- 📄 **Multiple Document Formats**: Support for PDF, DOCX, TXT files
- 🌐 **Web Content Extraction**: Extract content from web URLs
- 💬 **Natural Language Chat**: Ask questions about your documents in plain English
- ⚡ **Fast & Responsive**: Built with modern web technologies
- 🎨 **Beautiful UI**: Clean, modern interface with Tailwind CSS
- 🔒 **Type Safe**: Full TypeScript support

## Tech Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Testing**: Vitest
- **Linting**: ESLint

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ErrorBoundary.tsx
│   ├── Header.tsx
│   ├── Footer.tsx
│   └── Layout.tsx
├── pages/              # Page components
│   ├── HomePage.tsx
│   └── NotFoundPage.tsx
├── types/              # TypeScript type definitions
│   └── index.ts
├── utils/              # Utility functions
│   └── index.ts
├── hooks/              # Custom React hooks (future)
├── services/           # API services (future)
├── App.tsx             # Main app component
├── main.tsx           # App entry point
└── index.css          # Global styles
```

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd chatdoc-v3
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI

## Development Guidelines

### Code Style

- Use TypeScript for all new files
- Follow React functional component patterns
- Use Tailwind CSS for styling
- Implement proper error boundaries
- Write meaningful component and function names

### Component Organization

- **Components**: Reusable UI components
- **Pages**: Route-level components
- **Hooks**: Custom React hooks for shared logic
- **Services**: API calls and external integrations
- **Utils**: Pure utility functions
- **Types**: TypeScript type definitions

### Naming Conventions

- Components: PascalCase (e.g., `HomePage.tsx`)
- Files: PascalCase for components, camelCase for utilities
- Functions: camelCase
- Constants: UPPER_SNAKE_CASE
- CSS Classes: Tailwind utility classes

## Future Features

- [ ] Document upload functionality
- [ ] Web URL content extraction
- [ ] AI chat integration
- [ ] Document management
- [ ] User authentication
- [ ] Chat history
- [ ] Document search
- [ ] Export conversations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details